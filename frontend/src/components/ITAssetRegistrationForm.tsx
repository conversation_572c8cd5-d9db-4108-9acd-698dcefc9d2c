'use client'

import * as React from 'react'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { CalendarIcon, Loader2, Server, Network, Database, HardDrive, Shield, Cpu, MemoryStick, Wifi, QrCode, RefreshCw, Save } from 'lucide-react'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { format } from 'date-fns'
import { cn } from '@/lib/utils'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { toast } from '@/components/ui/use-toast'

// 表单验证模式
const assetFormSchema = z.object({
  // 基础信息
  name: z.string().min(2, '资产名称至少需要2个字符'),
  code: z.string().min(3, '资产编码至少需要3个字符'),
  category_id: z.number().min(1, '请选择资产分类').optional(),
  manufacturer: z.string().optional(),
  model: z.string().optional(),
  serial_number: z.string().optional(),
  status: z.enum(['active', 'maintenance', 'standby', 'offline', 'retired']),
  location: z.string().optional(),
  department: z.string().optional(),
  responsible_person: z.string().optional(),
  description: z.string().optional(),
  
  // 采购信息
  purchase_date: z.date().optional(),
  supplier: z.string().optional(),
  purchase_order_number: z.string().optional(),
  price: z.number().min(0).optional(),
  warranty_expire_date: z.date().optional(),
  asset_value: z.number().min(0).optional(),
  
  // 网络配置
  ip_address: z.string().regex(
    /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
    '请输入有效的IP地址'
  ).optional().or(z.literal('')),
  business_ip_address: z.string().regex(
    /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
    '请输入有效的业务IP地址'
  ).optional().or(z.literal('')),
  management_ip_address: z.string().regex(
    /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
    '请输入有效的管理IP地址'
  ).optional().or(z.literal('')),
  idrac_address: z.string().regex(
    /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
    '请输入有效的iDRAC管理地址'
  ).optional().or(z.literal('')),
  mac_address: z.string().regex(
    /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/,
    '请输入有效的MAC地址'
  ).optional().or(z.literal('')),
  hostname: z.string().optional(),
  domain: z.string().optional(),
  subnet_mask: z.string().optional(),
  gateway: z.string().optional(),
  dns_servers: z.string().optional(),
  vlan_id: z.number().min(1).max(4094).optional(),
  
  // 硬件规格
  cpu_model: z.string().optional(),
  cpu_cores: z.number().min(1).optional(),
  memory_size: z.number().min(1).optional(),
  storage_size: z.number().min(1).optional(),
  storage_type: z.enum(['SSD', 'HDD', 'NVMe', 'Hybrid']).optional(),
  port_count: z.number().min(1).optional(),
  port_speed: z.string().optional(),
  power_consumption: z.number().min(0).optional(),
  operating_temperature: z.string().optional(),
  
  // 网络耗材硬件配置
  memory_type: z.string().optional(), // 内存类型：DDR3, DDR4, DDR5
  memory_frequency: z.string().optional(), // 内存频率：2400MHz, 3200MHz
  memory_capacity: z.string().optional(), // 内存容量：8GB, 16GB, 32GB
  storage_interface: z.string().optional(), // 存储接口：SATA, NVMe, M.2
  storage_capacity: z.string().optional(), // 存储容量：256GB, 512GB, 1TB
  cpu_socket: z.string().optional(), // CPU插槽：LGA1151, AM4
  cpu_generation: z.string().optional(), // CPU代数：第10代, 第11代
  adapter_type: z.string().optional(), // 转接头类型：USB-C to HDMI, VGA to DVI
  connector_specification: z.string().optional(), // 连接器规格：USB 3.0, Type-C, Lightning
  
  // 软件信息
  operating_system: z.string().optional(),
  os_version: z.string().optional(),
  firmware_version: z.string().optional(),
  firmware_update_date: z.date().optional(),
  firmware_vendor: z.string().optional(),
  middleware_services: z.string().optional(),
  container_name: z.string().optional(),
  business_name: z.string().optional(),
  business_port: z.string().optional(),
  remarks: z.string().optional(),
  
  // 网络设备专用字段
  device_type: z.string().optional(),
  port_configuration: z.string().optional(),
  vlan_support: z.boolean().optional(),
  routing_protocols: z.string().optional(),
  management_interface: z.string().optional(),
  
  // 终端设备专用字段
  screen_size: z.string().optional(),
  screen_resolution: z.string().optional(),
  battery_capacity: z.string().optional(),
  wireless_support: z.string().optional(),
  peripheral_ports: z.string().optional(),
  
  // 服务器业务信息
  business_services: z.string().optional(),
  business_ports: z.string().optional(),
  service_ports: z.string().optional(),
  cluster_info: z.string().optional(),
  virtualization_platform: z.string().optional(),
  
  // 合同资产专用字段
  contract_number: z.string().optional(),
  contract_start_date: z.date().optional(),
  contract_end_date: z.date().optional(),
  contract_amount: z.number().min(0).optional(),
  contract_vendor: z.string().optional(),
  contract_type: z.string().optional(),
  auto_renewal: z.boolean().optional(),
  
  // 维保资产专用字段
  maintenance_level: z.string().optional(),
  response_time: z.string().optional(),
  maintenance_scope: z.string().optional(),
  maintenance_start_date: z.date().optional(),
  maintenance_end_date: z.date().optional(),
  
  // 授权信息
  license_type: z.string().optional(),
  license_key: z.string().optional(),
  license_start_date: z.date().optional(),
  license_end_date: z.date().optional(),
  license_user_count: z.number().min(1).optional(),
  license_concurrent_users: z.number().min(1).optional(),
  
  // 位置信息字段
  building: z.string().optional(),
  floor: z.string().optional(),
  room: z.string().optional(),
  rack_id: z.string().optional(),
  rack_position: z.string().optional(),
  rack_unit: z.number().optional(),
  
  // 机房管理字段（网络设备和服务器专用）
  area: z.string().optional(), // 区域
  management_scope: z.string().optional(), // 管理范围
  cold_aisle: z.enum(['cold_aisle_1', 'cold_aisle_2']).optional(), // 冷通道1或冷通道2
  cabinet_number: z.string().optional(), // 机柜号
  cabinet_u_position: z.string().optional(), // 机柜U位
  occupied_u_units: z.number().min(1).optional(), // 占用U位
  mains_pdu_number: z.string().optional(), // 市电PDU编号
  ups_pdu_number: z.string().optional(), // UPS PDU编号
  
  // 安全配置
  security_level: z.enum(['low', 'medium', 'high', 'critical']).optional(),
  encryption_enabled: z.boolean(),
  
  // 耗材相关字段
  is_consumable: z.boolean(),
  quantity: z.number().min(0).optional(),
  unit: z.string().optional(),
  min_stock_level: z.number().min(0).optional(),
})

type AssetFormData = z.infer<typeof assetFormSchema>

interface AssetCategory {
  id: number
  name: string
  code: string
  description?: string
  level: number
  parent_id?: number
}

interface ITAssetRegistrationFormProps {
  onSubmit: (data: AssetFormData) => Promise<void>
  initialData?: Partial<AssetFormData>
  isLoading?: boolean
  assetType?: 'server' | 'network' | 'storage' | 'other'
}

export function ITAssetRegistrationForm({ 
  onSubmit, 
  initialData, 
  isLoading = false,
  assetType = 'server'
}: ITAssetRegistrationFormProps) {
  const [activeTab, setActiveTab] = useState('basic')
  const [categories, setCategories] = useState<AssetCategory[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [autoGenerateCode, setAutoGenerateCode] = useState(true)

  const form = useForm<AssetFormData>({
    resolver: zodResolver(assetFormSchema),
    defaultValues: {
      name: '',
      code: '',
      category_id: undefined,
      status: 'active' as const,
      encryption_enabled: false,
      is_consumable: false,
      manufacturer: '',
      model: '',
      serial_number: '',
      location: '',
      department: '',
      responsible_person: '',
      description: '',
      supplier: '',
      purchase_order_number: '',
      hostname: '',
      domain: '',
      subnet_mask: '',
      gateway: '',
      dns_servers: '',
      cpu_model: '',
      storage_type: undefined,
      port_speed: '',
      operating_temperature: '',
      operating_system: '',
      os_version: '',
      firmware_version: '',
      firmware_vendor: '',
      middleware_services: '',
      container_name: '',
      business_name: '',
      business_port: '',
      remarks: '',
      device_type: '',
      port_configuration: '',
      routing_protocols: '',
      management_interface: '',
      screen_size: '',
      screen_resolution: '',
      battery_capacity: '',
      wireless_support: '',
      peripheral_ports: '',
      business_services: '',
      business_ports: '',
      service_ports: '',
      management_ip_address: '',
      cluster_info: '',
      virtualization_platform: '',
      contract_number: '',
      contract_vendor: '',
      contract_type: '',
      maintenance_level: '',
      response_time: '',
      maintenance_scope: '',
      license_type: '',
      license_key: '',
      building: '',
      floor: '',
      room: '',
      rack_id: '',
      rack_position: '',
      security_level: undefined,
      ...initialData
    }
  })

  // 监听category_id变化
  const watchedCategoryId = form.watch('category_id') || 0

  // 根据选择的资产分类获取图标
  const getAssetIcon = (categoryId?: number) => {
    const selectedCategoryId = categoryId || watchedCategoryId
    if (selectedCategoryId && categories.length > 0) {
      const selectedCategory = categories.find(c => c.id === selectedCategoryId)
      if (selectedCategory) {
        const categoryName = selectedCategory.name.toLowerCase()
        const categoryCode = selectedCategory.code.toLowerCase()
        
        if (categoryName.includes('服务器') || categoryCode.includes('srv')) {
          return <Server className="h-5 w-5" />
        } else if (categoryName.includes('网络') || categoryName.includes('交换') || categoryName.includes('路由') || categoryCode.includes('net')) {
          return <Network className="h-5 w-5" />
        } else if (categoryName.includes('存储') || categoryName.includes('硬盘') || categoryCode.includes('str')) {
          return <Database className="h-5 w-5" />
        } else if (categoryName.includes('安全') || categoryName.includes('防火墙')) {
          return <Shield className="h-5 w-5" />
        } else if (categoryName.includes('cpu') || categoryName.includes('处理器')) {
          return <Cpu className="h-5 w-5" />
        } else if (categoryName.includes('内存')) {
          return <MemoryStick className="h-5 w-5" />
        } else if (categoryName.includes('无线') || categoryName.includes('wifi')) {
          return <Wifi className="h-5 w-5" />
        }
      }
    }
    return <HardDrive className="h-5 w-5" />
  }

  // 根据选择的资产分类获取标题
  const getAssetTitle = (categoryId?: number) => {
    const selectedCategoryId = categoryId || watchedCategoryId
    if (selectedCategoryId && categories.length > 0) {
      const selectedCategory = categories.find(c => c.id === selectedCategoryId)
      if (selectedCategory) {
        return `${selectedCategory.name}资产登记`
      }
    }
    return '资产登记'
  }

  // 获取可见的标签页
  const getVisibleTabs = (categoryId?: number) => {
    const selectedCategoryId = categoryId || watchedCategoryId
    if (!selectedCategoryId || categories.length === 0) {
      return ['basic']
    }

    const selectedCategory = categories.find(c => c.id === selectedCategoryId)
    if (!selectedCategory) {
      return ['basic']
    }

    const categoryName = selectedCategory.name.toLowerCase()
    const categoryCode = selectedCategory.code.toLowerCase()

    // 根据不同的资产分类显示不同的标签页
    if (categoryName.includes('服务器') || categoryCode.includes('srv')) {
      return ['basic', 'purchase', 'network', 'hardware', 'software', 'business']
    } else if (categoryName.includes('打印耗材') || categoryName.includes('墨盒') || categoryName.includes('硒鼓') || categoryName.includes('碳粉') || categoryName.includes('打印纸')) {
      return ['basic', 'purchase', 'consumable']
    } else if (categoryName.includes('网络耗材') || categoryName.includes('内存条') || categoryName.includes('硬盘') || categoryName.includes('cpu') || categoryName.includes('处理器') || categoryName.includes('移动硬盘') || categoryName.includes('转接头') || categoryName.includes('其他配件') || categoryCode.includes('cons-net')) {
      return ['basic', 'purchase', 'hardware', 'consumable']
    } else if (categoryName.includes('办公耗材')) {
      return ['basic', 'purchase', 'consumable']
    } else if (categoryName.includes('安防') || categoryName.includes('监控') || categoryName.includes('摄像头') || categoryName.includes('录像机') || categoryName.includes('门禁') || categoryCode.includes('sec')) {
      return ['basic', 'purchase', 'network', 'hardware']
    } else if (categoryName.includes('网络') || categoryName.includes('交换') || categoryName.includes('路由') || categoryName.includes('防火墙') || categoryCode.includes('net')) {
      return ['basic', 'purchase', 'network', 'hardware', 'firmware']
    } else if (categoryName.includes('终端') || categoryName.includes('台式') || categoryName.includes('笔记本') || categoryCode.includes('end')) {
      return ['basic', 'purchase', 'hardware', 'terminal']
    } else if (categoryName.includes('消耗品') || categoryName.includes('耗材') || categoryCode.includes('cons')) {
      return ['basic', 'purchase', 'consumable']
    } else if (categoryName.includes('存储') || categoryCode.includes('str')) {
      return ['basic', 'purchase', 'hardware']
    } else if (categoryName.includes('软件') || categoryCode === 'sw') {
      return ['basic', 'purchase', 'software', 'license']
    } else if (categoryName.includes('合同') || categoryCode.includes('contract')) {
      return ['basic', 'contract', 'license']
    } else if (categoryName.includes('维保') || categoryCode.includes('maint')) {
      return ['basic', 'maintenance', 'license']
    } else {
      return ['basic', 'purchase']
    }
  }

  // 获取标签页配置
  const getTabConfig = () => {
    return {
      basic: { label: '基础信息', icon: <HardDrive className="h-4 w-4" /> },
      purchase: { label: '采购信息', icon: <QrCode className="h-4 w-4" /> },
      network: { label: '网络配置', icon: <Network className="h-4 w-4" /> },
      hardware: { label: '硬件规格', icon: <Cpu className="h-4 w-4" /> },
      software: { label: '软件信息', icon: <Database className="h-4 w-4" /> },
      firmware: { label: '固件信息', icon: <RefreshCw className="h-4 w-4" /> },
      terminal: { label: '终端规格', icon: <MemoryStick className="h-4 w-4" /> },
      business: { label: '业务信息', icon: <Server className="h-4 w-4" /> },
      contract: { label: '合同信息', icon: <QrCode className="h-4 w-4" /> },
      maintenance: { label: '维保信息', icon: <Shield className="h-4 w-4" /> },
      license: { label: '授权信息', icon: <Shield className="h-4 w-4" /> },
      consumable: { label: '耗材信息', icon: <MemoryStick className="h-4 w-4" /> }
    }
  }

  // 获取分类数据
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/asset-categories')
        if (response.ok) {
          const data = await response.json()
          setCategories(data)
        }
      } catch (error) {
        console.error('获取资产分类失败:', error)
        toast({
          title: '错误',
          description: '获取资产分类失败',
          variant: 'destructive'
        })
      }
    }
    fetchCategories()
  }, [])

  // 构建层级化的分类选项
  const buildHierarchicalCategories = (categories: AssetCategory[], parentId: number | null = null, level: number = 0): any[] => {
    const result: any[] = []
    const children = categories.filter(cat => cat.parent_id === parentId)
    
    children.forEach(category => {
      const indent = '　'.repeat(level)
      result.push({
        ...category,
        displayName: `${indent}${category.name}`
      })
      
      const subCategories = buildHierarchicalCategories(categories, category.id, level + 1)
      result.push(...subCategories)
    })
    
    return result
  }

  const hierarchicalCategories = buildHierarchicalCategories(categories)

  // 生成资产编码
  const generateAssetCode = (categoryId: number, name: string) => {
    const category = categories.find(c => c.id === categoryId)
    const timestamp = Date.now().toString().slice(-6)
    
    if (category) {
      const categoryPrefix = category.code.toUpperCase()
      const namePrefix = name.slice(0, 2).toUpperCase()
      return `${categoryPrefix}-${namePrefix}-${timestamp}`
    }
    
    return `AST-${name.slice(0, 2).toUpperCase()}-${timestamp}`
  }

  // 监听表单变化，自动生成编码
  useEffect(() => {
    const subscription = form.watch((value, { name: fieldName }) => {
      if (fieldName === 'category_id' || fieldName === 'name') {
        const categoryId = value.category_id as number
        const assetName = value.name as string
        
        if (categoryId && assetName && autoGenerateCode) {
          const newCode = generateAssetCode(categoryId, assetName)
          form.setValue('code', newCode)
        }
      }
    })
    
    return () => subscription.unsubscribe()
  }, [form, autoGenerateCode, categories])

  // 监听分类变化，自动切换标签页
  useEffect(() => {
    if (watchedCategoryId && categories.length > 0) {
      const visibleTabs = getVisibleTabs(watchedCategoryId)
      console.log('Selected Category ID:', watchedCategoryId)
      console.log('Visible Tabs:', visibleTabs)
      console.log('Active Tab:', activeTab)
      
      if (!visibleTabs.includes(activeTab)) {
        setActiveTab(visibleTabs[0])
      }
    }
  }, [watchedCategoryId, categories, activeTab])

  // 表单提交处理
  const handleSubmit = async (data: AssetFormData) => {
    setIsSubmitting(true)
    try {
      await onSubmit(data)
      toast({
        title: '成功',
        description: '资产登记成功'
      })
    } catch (error) {
      console.error('提交失败:', error)
      toast({
        title: '错误',
        description: '资产登记失败',
        variant: 'destructive'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // 渲染基础信息
  const renderBasicInfo = () => {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="category_id"
          render={({ field }) => (
            <FormItem>
              <FormLabel>资产分类 *</FormLabel>
              <Select onValueChange={(value) => field.onChange(parseInt(value))} value={field.value?.toString() || ''}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择资产分类" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {hierarchicalCategories.map((category) => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.displayName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem>
              <FormLabel>资产状态</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择资产状态" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="active">使用中</SelectItem>
                  <SelectItem value="maintenance">维护中</SelectItem>
                  <SelectItem value="standby">备用</SelectItem>
                  <SelectItem value="offline">离线</SelectItem>
                  <SelectItem value="retired">已退役</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>资产名称 *</FormLabel>
              <FormControl>
                <Input placeholder="请输入资产名称" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="code"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-2">
                资产编码 *
                <div className="flex items-center gap-2">
                  <Switch
                    checked={autoGenerateCode}
                    onCheckedChange={setAutoGenerateCode}
                  />
                  <span className="text-xs text-muted-foreground">自动生成</span>
                </div>
              </FormLabel>
              <FormControl>
                <Input 
                  placeholder="请输入资产编码" 
                  {...field} 
                  disabled={autoGenerateCode}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <FormField
          control={form.control}
          name="manufacturer"
          render={({ field }) => (
            <FormItem>
              <FormLabel>制造商</FormLabel>
              <FormControl>
                <Input placeholder="请输入制造商" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="model"
          render={({ field }) => (
            <FormItem>
              <FormLabel>型号</FormLabel>
              <FormControl>
                <Input placeholder="请输入型号" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="serial_number"
          render={({ field }) => (
            <FormItem>
              <FormLabel>序列号</FormLabel>
              <FormControl>
                <Input placeholder="请输入序列号" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <FormField
          control={form.control}
          name="location"
          render={({ field }) => (
            <FormItem>
              <FormLabel>位置</FormLabel>
              <FormControl>
                <Input placeholder="请输入位置" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="department"
          render={({ field }) => (
            <FormItem>
              <FormLabel>所属部门</FormLabel>
              <FormControl>
                <Input placeholder="请输入所属部门" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="responsible_person"
          render={({ field }) => (
            <FormItem>
              <FormLabel>责任人</FormLabel>
              <FormControl>
                <Input placeholder="请输入责任人" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <FormField
        control={form.control}
        name="description"
        render={({ field }) => (
          <FormItem>
            <FormLabel>描述</FormLabel>
            <FormControl>
              <Textarea
                placeholder="请输入资产描述..."
                className="min-h-[100px]"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      
      {/* 机房管理字段 - 仅对网络设备和服务器显示 */}
      {(() => {
        const selectedCategoryId = form.watch('category_id')
        const selectedCategory = categories.find(c => c.id === selectedCategoryId)
        const categoryName = selectedCategory?.name.toLowerCase() || ''
        const categoryCode = selectedCategory?.code.toLowerCase() || ''
        
        // 判断是否为网络设备或服务器
        const isNetworkOrServer = categoryName.includes('网络设备') || categoryName.includes('服务器') || categoryCode.includes('net-') || categoryCode.includes('server')
        
        if (!isNetworkOrServer) return null
        
        return (
          <div className="space-y-4">
            <div className="border-t pt-4">
              <h4 className="text-lg font-medium mb-4">机房管理信息</h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="area"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>区域</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择区域" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="2f_weak_current_room">2F 弱电间</SelectItem>
                          <SelectItem value="2f_gym">2F 健身房</SelectItem>
                          <SelectItem value="3f_data_center">3F 数据中心</SelectItem>
                          <SelectItem value="4f_weak_current_room">4F 弱电间</SelectItem>
                          <SelectItem value="7f_weak_current_room">7F 弱电间</SelectItem>
                          <SelectItem value="shanghai_office">上海办公区</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="management_scope"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>管理范围</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入管理范围" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <FormField
                  control={form.control}
                  name="cold_aisle"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>冷通道</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择冷通道" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="cold_aisle_1">冷通道1</SelectItem>
                          <SelectItem value="cold_aisle_2">冷通道2</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="cabinet_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>机柜号</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择机柜号" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Array.from({ length: 15 }, (_, i) => {
                            const cabinetNum = `A${String(i + 1).padStart(2, '0')}`
                            return (
                              <SelectItem key={cabinetNum} value={cabinetNum}>
                                {cabinetNum}
                              </SelectItem>
                            )
                          })}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="cabinet_u_position"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>机柜U位</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择机柜U位" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Array.from({ length: 42 }, (_, i) => {
                            const uPosition = String(i + 1)
                            return (
                              <SelectItem key={uPosition} value={uPosition}>
                                U{uPosition}
                              </SelectItem>
                            )
                          })}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <FormField
                  control={form.control}
                  name="occupied_u_units"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>占用U位</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          placeholder="请输入占用U位数量" 
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || undefined)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="mains_pdu_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>市电PDU编号</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择市电PDU编号" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Array.from({ length: 24 }, (_, i) => {
                            const pduNum = String(i + 1)
                            return (
                              <SelectItem key={pduNum} value={pduNum}>
                                {pduNum}
                              </SelectItem>
                            )
                          })}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="ups_pdu_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>UPS PDU编号</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择UPS PDU编号" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Array.from({ length: 24 }, (_, i) => {
                            const pduNum = String(i + 1)
                            return (
                              <SelectItem key={pduNum} value={pduNum}>
                                {pduNum}
                              </SelectItem>
                            )
                          })}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </div>
        )
      })()
    }
      </div>
    );
  }

  // 渲染采购信息
  const renderPurchaseInfo = () => {
    return (
      <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="purchase_date"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>采购日期</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full pl-3 text-left font-normal",
                        !field.value && "text-muted-foreground"
                      )}
                    >
                      {field.value ? (
                        format(field.value, "PPP")
                      ) : (
                        <span>选择日期</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    disabled={(date) =>
                      date > new Date() || date < new Date("1900-01-01")
                    }
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="warranty_expire_date"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>保修到期日期</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full pl-3 text-left font-normal",
                        !field.value && "text-muted-foreground"
                      )}
                    >
                      {field.value ? (
                        format(field.value, "PPP")
                      ) : (
                        <span>选择日期</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    disabled={(date) => date < new Date()}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="supplier"
          render={({ field }) => (
            <FormItem>
              <FormLabel>供应商</FormLabel>
              <FormControl>
                <Input placeholder="请输入供应商" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="purchase_order_number"
          render={({ field }) => (
            <FormItem>
              <FormLabel>采购订单号</FormLabel>
              <FormControl>
                <Input placeholder="请输入采购订单号" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="price"
          render={({ field }) => (
            <FormItem>
              <FormLabel>采购价格</FormLabel>
              <FormControl>
                <Input 
                  type="number" 
                  placeholder="请输入采购价格" 
                  {...field}
                  onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="asset_value"
          render={({ field }) => (
            <FormItem>
              <FormLabel>资产价值</FormLabel>
              <FormControl>
                <Input 
                  type="number" 
                  placeholder="请输入资产价值" 
                  {...field}
                  onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        </div>
      </div>
    );
  }

  // 渲染网络配置
  const renderNetworkInfo = () => {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <FormField
          control={form.control}
          name="ip_address"
          render={({ field }) => (
            <FormItem>
              <FormLabel>IP地址</FormLabel>
              <FormControl>
                <Input placeholder="*************" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="business_ip_address"
          render={({ field }) => (
            <FormItem>
              <FormLabel>业务IP地址</FormLabel>
              <FormControl>
                <Input placeholder="**********" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="management_ip_address"
          render={({ field }) => (
            <FormItem>
              <FormLabel>管理IP地址</FormLabel>
              <FormControl>
                <Input placeholder="************" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="mac_address"
          render={({ field }) => (
            <FormItem>
              <FormLabel>MAC地址</FormLabel>
              <FormControl>
                <Input placeholder="00:11:22:33:44:55" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="hostname"
          render={({ field }) => (
            <FormItem>
              <FormLabel>主机名</FormLabel>
              <FormControl>
                <Input placeholder="server-001" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <FormField
          control={form.control}
          name="subnet_mask"
          render={({ field }) => (
            <FormItem>
              <FormLabel>子网掩码</FormLabel>
              <FormControl>
                <Input placeholder="*************" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="gateway"
          render={({ field }) => (
            <FormItem>
              <FormLabel>网关</FormLabel>
              <FormControl>
                <Input placeholder="192.168.1.1" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="dns_servers"
          render={({ field }) => (
            <FormItem>
              <FormLabel>DNS服务器</FormLabel>
              <FormControl>
                <Input placeholder="8.8.8.8, 8.8.4.4" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        </div>
      </div>
    );
  }

  // 渲染硬件规格
  const renderHardwareInfo = () => {
    const selectedCategoryId = form.watch('category_id')
    const selectedCategory = categories.find(c => c.id === selectedCategoryId)
    const categoryName = selectedCategory?.name.toLowerCase() || ''
    const categoryCode = selectedCategory?.code.toLowerCase() || ''
    
    // 判断是否为网络耗材
    const isNetworkConsumable = categoryName.includes('网络耗材') || categoryName.includes('内存条') || categoryName.includes('硬盘') || categoryName.includes('cpu') || categoryName.includes('处理器') || categoryName.includes('移动硬盘') || categoryName.includes('转接头') || categoryName.includes('其他配件') || categoryCode.includes('cons-net')
    
    if (isNetworkConsumable) {
      return (
        <div className="space-y-6">
          {/* 内存条配置 */}
          {(categoryName.includes('内存') || categoryName.includes('ram')) && (
            <div className="space-y-4">
              <h4 className="text-lg font-medium">内存条配置</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="memory_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>内存类型</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择内存类型" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="DDR3">DDR3</SelectItem>
                          <SelectItem value="DDR4">DDR4</SelectItem>
                          <SelectItem value="DDR5">DDR5</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="memory_frequency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>内存频率</FormLabel>
                      <FormControl>
                        <Input placeholder="2400MHz, 3200MHz" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="memory_capacity"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>内存容量</FormLabel>
                      <FormControl>
                        <Input placeholder="8GB, 16GB, 32GB" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          )}
          
          {/* 硬盘配置 */}
          {(categoryName.includes('硬盘') || categoryName.includes('移动硬盘')) && (
            <div className="space-y-4">
              <h4 className="text-lg font-medium">存储设备配置</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="storage_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>存储类型</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择存储类型" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="SSD">固态硬盘(SSD)</SelectItem>
                          <SelectItem value="HDD">机械硬盘(HDD)</SelectItem>
                          <SelectItem value="NVMe">NVMe SSD</SelectItem>
                          <SelectItem value="Hybrid">混合硬盘</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="storage_interface"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>接口类型</FormLabel>
                      <FormControl>
                        <Input placeholder="SATA, NVMe, M.2, USB 3.0" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="storage_capacity"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>存储容量</FormLabel>
                      <FormControl>
                        <Input placeholder="256GB, 512GB, 1TB" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          )}
          
          {/* CPU配置 */}
          {(categoryName.includes('cpu') || categoryName.includes('处理器')) && (
            <div className="space-y-4">
              <h4 className="text-lg font-medium">CPU处理器配置</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="cpu_model"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>CPU型号</FormLabel>
                      <FormControl>
                        <Input placeholder="Intel i7-12700K, AMD Ryzen 7 5800X" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="cpu_socket"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>CPU插槽</FormLabel>
                      <FormControl>
                        <Input placeholder="LGA1700, AM4, LGA1151" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="cpu_generation"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>CPU代数</FormLabel>
                      <FormControl>
                        <Input placeholder="第12代, 第11代, Zen3" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          )}
          
          {/* 转接头配置 */}
          {categoryName.includes('转接头') && (
            <div className="space-y-4">
              <h4 className="text-lg font-medium">转接头配置</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="adapter_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>转接头类型</FormLabel>
                      <FormControl>
                        <Input placeholder="USB-C to HDMI, VGA to DVI, Type-C to USB" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="connector_specification"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>连接器规格</FormLabel>
                      <FormControl>
                        <Input placeholder="USB 3.0, Type-C, Lightning, Thunderbolt" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          )}
          
          {/* 通用硬件信息 */}
          <div className="space-y-4">
            <h4 className="text-lg font-medium">通用硬件信息</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="manufacturer"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>制造商</FormLabel>
                    <FormControl>
                      <Input placeholder="Intel, AMD, Kingston, Samsung" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="model"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>产品型号</FormLabel>
                    <FormControl>
                      <Input placeholder="具体产品型号" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
        </div>
      )

    // 默认硬件配置（非网络耗材）
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="cpu_model"
            render={({ field }) => (
              <FormItem>
                <FormLabel>CPU型号</FormLabel>
                <FormControl>
                  <Input placeholder="Intel Xeon E5-2680 v4" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="cpu_cores"
            render={({ field }) => (
              <FormItem>
                <FormLabel>CPU核心数</FormLabel>
                <FormControl>
                  <Input 
                    type="number" 
                    placeholder="16" 
                    {...field}
                    onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="memory_size"
            render={({ field }) => (
              <FormItem>
                <FormLabel>内存大小 (GB)</FormLabel>
                <FormControl>
                  <Input 
                    type="number" 
                    placeholder="64" 
                    {...field}
                    onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="storage_size"
            render={({ field }) => (
              <FormItem>
                <FormLabel>存储大小 (GB)</FormLabel>
                <FormControl>
                  <Input 
                    type="number" 
                    placeholder="1000" 
                    {...field}
                    onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="storage_type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>存储类型</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="请选择存储类型" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="SSD">SSD</SelectItem>
                    <SelectItem value="HDD">HDD</SelectItem>
                    <SelectItem value="NVMe">NVMe</SelectItem>
                    <SelectItem value="Hybrid">混合</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="power_consumption"
            render={({ field }) => (
              <FormItem>
                <FormLabel>功耗 (W)</FormLabel>
                <FormControl>
                  <Input 
                    type="number" 
                    placeholder="300" 
                    {...field}
                    onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>
    </div>
  )

  // 渲染软件信息
  const renderSoftwareInfo = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="operating_system"
          render={({ field }) => (
            <FormItem>
              <FormLabel>操作系统</FormLabel>
              <FormControl>
                <Input placeholder="Ubuntu 20.04 LTS" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="os_version"
          render={({ field }) => (
            <FormItem>
              <FormLabel>系统版本</FormLabel>
              <FormControl>
                <Input placeholder="20.04.3" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="firmware_version"
          render={({ field }) => (
            <FormItem>
              <FormLabel>固件版本</FormLabel>
              <FormControl>
                <Input placeholder="v2.1.3" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="firmware_vendor"
          render={({ field }) => (
            <FormItem>
              <FormLabel>固件厂商</FormLabel>
              <FormControl>
                <Input placeholder="Cisco, Huawei, H3C" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 gap-4">
        <FormField
          control={form.control}
          name="firmware_update_date"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>固件更新日期</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={"outline"}
                      className={
                        cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )
                      }
                    >
                      {field.value ? (
                        format(field.value, "PPP")
                      ) : (
                        <span>选择固件更新日期</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    disabled={(date) => date > new Date()}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="middleware_services"
          render={({ field }) => (
            <FormItem>
              <FormLabel>中间件服务</FormLabel>
              <FormControl>
                <Input placeholder="Nginx, Redis, MySQL" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="container_name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>容器名称</FormLabel>
              <FormControl>
                <Input placeholder="web-app-container" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="business_name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>业务名称</FormLabel>
              <FormControl>
                <Input placeholder="用户管理系统" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <FormField
        control={form.control}
        name="business_port"
        render={({ field }) => (
          <FormItem>
            <FormLabel>业务端口</FormLabel>
            <FormControl>
              <Input placeholder="8080, 8443" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="remarks"
        render={({ field }) => (
          <FormItem>
            <FormLabel>备注</FormLabel>
            <FormControl>
              <Textarea
                placeholder="请输入备注信息..."
                className="min-h-[100px]"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      </div>
    </div>
  )

  // 渲染固件信息
  const renderFirmwareInfo = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="firmware_version"
          render={({ field }) => (
            <FormItem>
              <FormLabel>固件版本</FormLabel>
              <FormControl>
                <Input placeholder="v2.1.3" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="firmware_vendor"
          render={({ field }) => (
            <FormItem>
              <FormLabel>固件厂商</FormLabel>
              <FormControl>
                <Input placeholder="Cisco, Huawei, H3C" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="firmware_update_date"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>固件更新日期</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={"outline"}
                      className={
                        cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )
                      }
                    >
                      {field.value ? (
                        format(field.value, "PPP")
                      ) : (
                        <span>选择固件更新日期</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    disabled={(date) => date > new Date()}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="device_type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>设备类型</FormLabel>
              <FormControl>
                <Input placeholder="核心交换机, 接入交换机" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="management_interface"
          render={({ field }) => (
            <FormItem>
              <FormLabel>管理接口类型</FormLabel>
              <FormControl>
                <Input placeholder="Console, SSH, Telnet, Web" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="vlan_support"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">VLAN支持</FormLabel>
                <FormDescription>
                  设备是否支持VLAN功能
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />
      </div>

      <FormField
        control={form.control}
        name="routing_protocols"
        render={({ field }) => (
          <FormItem>
            <FormLabel>支持的路由协议</FormLabel>
            <FormControl>
              <Input placeholder="OSPF, BGP, RIP, EIGRP" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="port_configuration"
        render={({ field }) => (
          <FormItem>
            <FormLabel>端口配置信息</FormLabel>
            <FormControl>
              <Textarea
                placeholder="请输入端口配置详情，如端口数量、类型、速率等..."
                className="min-h-[100px]"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      </div>
    )

  // 渲染终端规格信息
  const renderTerminalInfo = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="screen_size"
          render={({ field }) => (
            <FormItem>
              <FormLabel>屏幕尺寸</FormLabel>
              <FormControl>
                <Input placeholder="15.6英寸, 24英寸" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="screen_resolution"
          render={({ field }) => (
            <FormItem>
              <FormLabel>屏幕分辨率</FormLabel>
              <FormControl>
                <Input placeholder="1920x1080, 2560x1440" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="battery_capacity"
          render={({ field }) => (
            <FormItem>
              <FormLabel>电池容量</FormLabel>
              <FormControl>
                <Input placeholder="4000mAh, 6芯锂电池" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="wireless_support"
          render={({ field }) => (
            <FormItem>
              <FormLabel>无线支持</FormLabel>
              <FormControl>
                <Input placeholder="WiFi 6, 蓝牙 5.0, NFC" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <FormField
        control={form.control}
        name="peripheral_ports"
        render={({ field }) => (
          <FormItem>
            <FormLabel>外设接口</FormLabel>
            <FormControl>
              <Textarea
                placeholder="USB 3.0 x4, HDMI x1, Type-C x2, 3.5mm音频接口..."
                className="min-h-[100px]"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      </div>
    )

  // 渲染业务信息
  const renderBusinessInfo = (): JSX.Element => {
    return (
      <div className="space-y-6">
      <div className="space-y-4">
        <h4 className="text-lg font-medium">业务服务配置</h4>
        <FormField
          control={form.control}
          name="business_services"
          render={({ field }) => (
            <FormItem>
              <FormLabel>业务服务列表</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="请输入运行的业务服务，每行一个：\n用户管理系统\n订单处理系统\n数据分析平台\nAPI网关服务"
                  className="min-h-[120px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="business_ports"
          render={({ field }) => (
            <FormItem>
              <FormLabel>业务端口配置</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="请输入业务端口配置，每行一个服务及其端口：\n用户管理系统:8080\n订单处理系统:8081\nAPI网关:8082\n数据分析平台:8083\n监控服务:9090"
                  className="min-h-[120px]"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                格式：服务名称:端口号，每行一个服务
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="service_ports"
          render={({ field }) => (
            <FormItem>
              <FormLabel>其他端口映射</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="其他端口映射信息，如负载均衡、代理端口等"
                  className="min-h-[80px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="space-y-4">
        <h4 className="text-lg font-medium">集群与虚拟化配置</h4>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="cluster_info"
          render={({ field }) => (
            <FormItem>
              <FormLabel>集群信息</FormLabel>
              <FormControl>
                <Input placeholder="Kubernetes集群, Docker Swarm" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="virtualization_platform"
          render={({ field }) => (
            <FormItem>
              <FormLabel>虚拟化平台</FormLabel>
              <FormControl>
                <Input placeholder="VMware vSphere, Hyper-V, KVM" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
      </div>
    );
  }

  // 渲染合同信息
  const renderContractInfo = () => {
    return (
      <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="contract_number"
          render={({ field }) => (
            <FormItem>
              <FormLabel>合同编号</FormLabel>
              <FormControl>
                <Input placeholder="CT-2024-001" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="contract_type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>合同类型</FormLabel>
              <FormControl>
                <Input placeholder="软件许可, 云服务, 技术支持" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="contract_start_date"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>合同开始日期</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={"outline"}
                      className={
                        cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )
                      }
                    >
                      {field.value ? (
                        format(field.value, "PPP")
                      ) : (
                        <span>选择合同开始日期</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="contract_end_date"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>合同到期日期</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={"outline"}
                      className={
                        cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )
                      }
                    >
                      {field.value ? (
                        format(field.value, "PPP")
                      ) : (
                        <span>选择合同到期日期</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    disabled={(date) => date < new Date()}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="contract_amount"
          render={({ field }) => (
            <FormItem>
              <FormLabel>合同金额</FormLabel>
              <FormControl>
                <Input 
                  type="number" 
                  placeholder="100000" 
                  {...field}
                  onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="contract_vendor"
          render={({ field }) => (
            <FormItem>
              <FormLabel>合同供应商</FormLabel>
              <FormControl>
                <Input placeholder="Microsoft, Oracle, SAP" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <FormField
        control={form.control}
        name="auto_renewal"
        render={({ field }) => (
          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
            <div className="space-y-0.5">
              <FormLabel className="text-base">自动续约</FormLabel>
              <FormDescription>
                合同是否设置为自动续约
              </FormDescription>
            </div>
            <FormControl>
              <Switch
                checked={field.value}
                onCheckedChange={field.onChange}
              />
            </FormControl>
          </FormItem>
        )}
      />
      </div>
      </div>
    )
  }

  // 渲染维保信息
  const renderMaintenanceInfo = () => {
    return (
      <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="maintenance_level"
          render={({ field }) => (
            <FormItem>
              <FormLabel>维保级别</FormLabel>
              <FormControl>
                <Input placeholder="标准, 高级, 7x24" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="response_time"
          render={({ field }) => (
            <FormItem>
              <FormLabel>响应时间</FormLabel>
              <FormControl>
                <Input placeholder="4小时, 24小时, 72小时" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="maintenance_start_date"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>维保开始日期</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={"outline"}
                      className={
                        cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )
                      }
                    >
                      {field.value ? (
                        format(field.value, "PPP")
                      ) : (
                        <span>选择维保开始日期</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="maintenance_end_date"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>维保到期日期</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={"outline"}
                      className={
                        cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )
                      }
                    >
                      {field.value ? (
                        format(field.value, "PPP")
                      ) : (
                        <span>选择维保到期日期</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    disabled={(date) => date < new Date()}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <FormField
        control={form.control}
        name="maintenance_scope"
        render={({ field }) => (
          <FormItem>
            <FormLabel>维保范围</FormLabel>
            <FormControl>
              <Textarea
                placeholder="硬件故障维修, 软件技术支持, 系统升级, 远程诊断..."
                className="min-h-[100px]"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      </div>
    )
  }

  // 渲染授权信息
  const renderLicenseInfo = () => {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="license_type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>许可证类型</FormLabel>
              <FormControl>
                <Input placeholder="永久许可, 订阅许可, 试用许可" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="license_key"
          render={({ field }) => (
            <FormItem>
              <FormLabel>许可证密钥</FormLabel>
              <FormControl>
                <Input placeholder="XXXX-XXXX-XXXX-XXXX" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="license_start_date"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>授权开始日期</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={"outline"}
                      className={
                        cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )
                      }
                    >
                      {field.value ? (
                        format(field.value, "PPP")
                      ) : (
                        <span>选择授权开始日期</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="license_end_date"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>授权到期日期</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={"outline"}
                      className={
                        cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )
                      }
                    >
                      {field.value ? (
                        format(field.value, "PPP")
                      ) : (
                        <span>选择授权到期日期</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    disabled={(date) => date < new Date()}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="license_user_count"
          render={({ field }) => (
            <FormItem>
              <FormLabel>授权用户数</FormLabel>
              <FormControl>
                <Input 
                  type="number" 
                  placeholder="100" 
                  {...field}
                  onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="license_concurrent_users"
          render={({ field }) => (
            <FormItem>
              <FormLabel>并发用户数</FormLabel>
              <FormControl>
                <Input 
                  type="number" 
                  placeholder="50" 
                  {...field}
                  onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
      </div>
    )
  }

  // 渲染耗材信息
  const renderConsumableInfo = () => {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <FormField
          control={form.control}
          name="quantity"
          render={({ field }) => (
            <FormItem>
              <FormLabel>数量 *</FormLabel>
              <FormControl>
                <Input 
                  type="number" 
                  placeholder="100" 
                  {...field}
                  onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="unit"
          render={({ field }) => (
            <FormItem>
              <FormLabel>单位 *</FormLabel>
              <Select onValueChange={field.onChange} value={field.value || ''}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择单位" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="个">个</SelectItem>
                  <SelectItem value="盒">盒</SelectItem>
                  <SelectItem value="包">包</SelectItem>
                  <SelectItem value="张">张</SelectItem>
                  <SelectItem value="条">条</SelectItem>
                  <SelectItem value="根">根</SelectItem>
                  <SelectItem value="套">套</SelectItem>
                  <SelectItem value="台">台</SelectItem>
                  <SelectItem value="片">片</SelectItem>
                  <SelectItem value="卷">卷</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="min_stock_level"
          render={({ field }) => (
            <FormItem>
              <FormLabel>最低库存预警</FormLabel>
              <FormControl>
                <Input 
                  type="number" 
                  placeholder="10" 
                  {...field}
                  onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                />
              </FormControl>
              <FormDescription>
                当库存低于此数量时将发出预警
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="is_consumable"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">
                  标记为耗材
                </FormLabel>
                <FormDescription>
                  启用后将按耗材方式管理库存
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />
      </div>
      </div>
    );
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {watchedCategoryId ? getAssetIcon(watchedCategoryId) : <HardDrive className="h-5 w-5" />}
          {watchedCategoryId ? getAssetTitle(watchedCategoryId) : '资产登记'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              {(() => {
                const visibleTabs = getVisibleTabs(watchedCategoryId)
                const tabConfig = getTabConfig()
                
                return (
                  <>
                    <TabsList className={`grid w-full ${
                      visibleTabs.length === 1 ? 'grid-cols-1' :
                      visibleTabs.length === 2 ? 'grid-cols-2' :
                      visibleTabs.length === 3 ? 'grid-cols-3' :
                      visibleTabs.length === 4 ? 'grid-cols-4' :
                      'grid-cols-5'
                    }`}>
                      {visibleTabs.map((tabKey) => {
                        const config = tabConfig[tabKey as keyof typeof tabConfig]
                        return (
                          <TabsTrigger key={tabKey} value={tabKey} className="flex items-center gap-2">
                            {config.icon}
                            {config.label}
                          </TabsTrigger>
                        )
                      })}
                    </TabsList>
                    
                    {visibleTabs.includes('basic') && (
                      <TabsContent value="basic" className="space-y-4">
                        {renderBasicInfo()}
                      </TabsContent>
                    )}
                    
                    {visibleTabs.includes('purchase') && (
                      <TabsContent value="purchase" className="space-y-4">
                        {renderPurchaseInfo()}
                      </TabsContent>
                    )}
                    
                    {visibleTabs.includes('network') && (
                      <TabsContent value="network" className="space-y-4">
                        {renderNetworkInfo()}
                      </TabsContent>
                    )}
                    
                    {visibleTabs.includes('hardware') && (
                      <TabsContent value="hardware" className="space-y-4">
                        {renderHardwareInfo()}
                      </TabsContent>
                    )}
                    
                    {visibleTabs.includes('software') && (
                      <TabsContent value="software" className="space-y-4">
                        {renderSoftwareInfo()}
                      </TabsContent>
                    )}
                    
                    {visibleTabs.includes('firmware') && (
                      <TabsContent value="firmware" className="space-y-4">
                        {renderFirmwareInfo()}
                      </TabsContent>
                    )}
                    
                    {visibleTabs.includes('terminal') && (
                      <TabsContent value="terminal" className="space-y-4">
                        {renderTerminalInfo()}
                      </TabsContent>
                    )}
                    
                    {visibleTabs.includes('business') && (
                      <TabsContent value="business" className="space-y-4">
                        {renderBusinessInfo()}
                      </TabsContent>
                    )}
                    
                    {visibleTabs.includes('contract') && (
                      <TabsContent value="contract" className="space-y-4">
                        {renderContractInfo()}
                      </TabsContent>
                    )}
                    
                    {visibleTabs.includes('maintenance') && (
                      <TabsContent value="maintenance" className="space-y-4">
                        {renderMaintenanceInfo()}
                      </TabsContent>
                    )}
                    
                    {visibleTabs.includes('license') && (
                      <TabsContent value="license" className="space-y-4">
                        {renderLicenseInfo()}
                      </TabsContent>
                    )}
                    
                    {visibleTabs.includes('consumable') && (
                      <TabsContent value="consumable" className="space-y-4">
                        {renderConsumableInfo()}
                      </TabsContent>
                    )}
                  </>
                )
              })()}
            </Tabs>

            <div className="flex justify-between pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => form.reset()}
                disabled={isSubmitting}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                重置表单
              </Button>
              
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    const formData = form.getValues()
                    localStorage.setItem('asset_draft', JSON.stringify(formData))
                    toast({
                      title: '草稿已保存',
                      description: '表单数据已保存到本地'
                    })
                  }}
                  disabled={isSubmitting}
                >
                  保存草稿
                </Button>
                
                <Button 
                  type="submit" 
                  disabled={isSubmitting || isLoading}
                  className="min-w-[120px]"
                >
                  {isSubmitting ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      登记中...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      登记资产
                    </>
                  )}
                </Button>
              </div>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}

export default ITAssetRegistrationForm;
