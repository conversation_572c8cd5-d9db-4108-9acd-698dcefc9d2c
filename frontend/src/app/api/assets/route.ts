import { query } from '@/lib/db'
import { NextResponse } from 'next/server'
import { Asset, SNMPConfig, SSHConfig } from '@/types/asset'

// 获取资产列表
export async function GET() {
  try {
    const assets = await query(`
      SELECT a.*, 
             m.enabled as monitoring_enabled,
             m.type as monitoring_type
      FROM assets a
      LEFT JOIN monitored_devices m ON a.id = m.asset_id
      ORDER BY a.created_at DESC
    `)
    return NextResponse.json(assets)
  } catch (err) {
    return NextResponse.json(
      { error: '获取资产列表失败' },
      { status: 500 }
    )
  }
}

// 创建新资产
export async function POST(request: Request) {
  try {
    const assetData: Asset = await request.json()
    
    // 插入资产基础信息
    const result = await query(
      `INSERT INTO assets 
      (name, type, ip_address, mac_address, location, description) 
      VALUES ($1, $2, $3, $4, $5, $6) RETURNING id`,
      [
        assetData.name,
        assetData.type,
        assetData.ipAddress,
        assetData.macAddress,
        assetData.location,
        assetData.description
      ]
    )
    
    const assetId = result[0].id
    
    // 如果有监控配置，插入监控设备表
    if (assetData.monitoringConfig?.enabled && assetData.monitoringConfig.type !== 'none') {
      const config = assetData.monitoringConfig.config
      
      if (assetData.monitoringConfig.type === 'snmp') {
        const snmpConfig = config as SNMPConfig
        await query(
          `INSERT INTO monitored_devices 
          (asset_id, host, type, community, oids, interval) 
          VALUES ($1, $2, 'snmp', $3, $4, $5)`,
          [
            assetId,
            assetData.ipAddress,
            snmpConfig.community,
            snmpConfig.oids,
            snmpConfig.interval
          ]
        )
      } else {
        const sshConfig = config as SSHConfig
        await query(
          `INSERT INTO monitored_devices 
          (asset_id, host, type, username, password, commands, interval, port) 
          VALUES ($1, $2, 'ssh', $3, $4, $5, $6, $7)`,
          [
            assetId,
            assetData.ipAddress,
            sshConfig.username,
            sshConfig.password,
            sshConfig.commands,
            sshConfig.interval,
            sshConfig.port || 22
          ]
        )
      }
    }
    
    return NextResponse.json({ success: true, id: assetId })
  } catch (err) {
    return NextResponse.json(
      { error: '创建资产失败' },
      { status: 500 }
    )
  }
}