import { NextResponse } from 'next/server'
import fs from 'fs'
import path from 'path'

// 获取资产分类列表
export async function GET() {
  try {
    // 从 categories.json 文件读取数据
    const categoriesPath = path.join(process.cwd(), '..', 'categories.json')
    const categoriesData = fs.readFileSync(categoriesPath, 'utf8')
    const categories = JSON.parse(categoriesData)
    
    // 为每个分类添加 attributes 字段以保持兼容性
    const categoriesWithAttributes = categories.map((category: any) => ({
      ...category,
      attributes: category.attributes || {
        codeRule: {
          digits: 4,
          prefix: category.code?.split('-').pop() || 'ASSET'
        },
        required: []
      }
    }))
    
    return NextResponse.json(categoriesWithAttributes)
  } catch (err) {
    console.error('获取资产分类失败:', err)
    return NextResponse.json(
      { error: '获取资产分类失败' },
      { status: 500 }
    )
  }
}

// 创建新的资产分类
export async function POST(request: Request) {
  try {
    const categoryData = await request.json()
    
    // 读取现有分类数据
    const categoriesPath = path.join(process.cwd(), '..', 'categories.json')
    const categoriesData = fs.readFileSync(categoriesPath, 'utf8')
    const categories = JSON.parse(categoriesData)
    
    // 生成新的ID
    const maxId = Math.max(...categories.map((cat: any) => cat.id || 0))
    const newCategory = {
      ...categoryData,
      id: maxId + 1,
      created_at: new Date().toISOString(),
      updated_at: null
    }
    
    // 添加新分类到数组
    categories.push(newCategory)
    
    // 写回文件
    fs.writeFileSync(categoriesPath, JSON.stringify(categories, null, 2))
    
    return NextResponse.json(newCategory)
  } catch (err) {
    console.error('创建资产分类失败:', err)
    return NextResponse.json(
      { error: '创建资产分类失败' },
      { status: 500 }
    )
  }
}