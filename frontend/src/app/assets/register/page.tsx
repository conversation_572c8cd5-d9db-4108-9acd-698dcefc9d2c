'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import ITAssetRegistrationForm from '@/components/ITAssetRegistrationForm'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Plus, Upload, Download, FileText } from 'lucide-react'
import Link from 'next/link'
import { toast } from '@/components/ui/use-toast'
import {
  Ta<PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function AssetRegisterPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('single')

  const handleSubmit = async (data: any) => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/assets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (response.ok) {
        const result = await response.json()
        toast({
          title: '成功',
          description: `资产 "${result.name}" 已成功登记`,
        })
        router.push('/assets')
      } else {
        const error = await response.json()
        throw new Error(error.detail || 'Failed to create asset')
      }
    } catch (error) {
      console.error('Error creating asset:', error)
      toast({
        title: '错误',
        description: error instanceof Error ? error.message : '资产登记失败，请重试',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleBulkImport = async (file: File) => {
    // TODO: 实现批量导入功能
    toast({
      title: '功能开发中',
      description: '批量导入功能正在开发中，敬请期待',
    })
  }

  const downloadTemplate = () => {
    // TODO: 实现模板下载功能
    toast({
      title: '功能开发中',
      description: '模板下载功能正在开发中，敬请期待',
    })
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div>
          <Link href="/assets">
            <Button variant="outline" className="mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回资产列表
            </Button>
          </Link>
          <h1 className="text-3xl font-bold">资产登记</h1>
          <p className="text-muted-foreground mt-2">
            添加新的IT资产到系统中，支持单个登记和批量导入
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" onClick={downloadTemplate}>
            <Download className="h-4 w-4 mr-2" />
            下载模板
          </Button>
          <Link href="/assets/categories">
            <Button variant="outline">
              <FileText className="h-4 w-4 mr-2" />
              管理分类
            </Button>
          </Link>
        </div>
      </div>

      {/* 登记方式选择 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 max-w-md">
          <TabsTrigger value="single" className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            单个登记
          </TabsTrigger>
          <TabsTrigger value="bulk" className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            批量导入
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="single" className="mt-6">
          <ITAssetRegistrationForm 
            onSubmit={handleSubmit} 
            isLoading={isLoading} 
          />
        </TabsContent>
        
        <TabsContent value="bulk" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                批量导入资产
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
                <Upload className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  批量导入功能开发中
                </h3>
                <p className="text-gray-500 mb-4">
                  支持Excel文件批量导入资产信息，敬请期待
                </p>
                <div className="flex justify-center gap-4">
                  <Button variant="outline" onClick={downloadTemplate}>
                    <Download className="h-4 w-4 mr-2" />
                    下载导入模板
                  </Button>
                  <Button disabled>
                    <Upload className="h-4 w-4 mr-2" />
                    选择文件导入
                  </Button>
                </div>
              </div>
              
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">导入说明：</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• 请先下载导入模板，按照模板格式填写资产信息</li>
                  <li>• 支持Excel (.xlsx) 格式文件</li>
                  <li>• 资产编码必须唯一，重复编码将导致导入失败</li>
                  <li>• IP地址和MAC地址格式必须正确</li>
                  <li>• 资产分类必须在系统中已存在</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}