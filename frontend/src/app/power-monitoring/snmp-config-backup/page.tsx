'use client'

import React from 'react'
import { <PERSON>, CardHeader, CardBody, CardFooter, But<PERSON> } from '@nextui-org/react'

export default function SNMPConfigPage() {
  // 基础状态
  const [items, setItems] = useState<SNMPItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // 搜索和筛选状态
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState<string>('all')
  const [selectedLocation, setSelectedLocation] = useState<string>('all')
  const [statusFilter, setStatusFilter] = useState<string>('all')

  // 模态框状态
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [currentItem, setCurrentItem] = useState<SNMPItem | undefined>(undefined)
  const [modalTitle, setModalTitle] = useState('添加SNMP监控项')

  // 分页状态
  const [page, setPage] = useState(1)
  const rowsPerPage = 10

  // 排序状态
  const [sortDescriptor, setSortDescriptor] = useState<SortDescriptor>({
    column: "name",
    direction: "ascending"
  })

  // 加载SNMP监控项
  useEffect(() => {
    const fetchItems = async () => {
      try {
        setLoading(true)
        const data = await getSNMPItems()
        setItems(data)
        setError(null)
      } catch (err) {
        setError('加载SNMP监控项失败')
        console.error(err)
      } finally {
        setLoading(false)
      }
    }
    fetchItems()
  }, [])

  // 过滤和排序监控项
  const filteredItems = useMemo(() => {
    return items.filter(item => {
      const matchesSearch =
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.key.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.oid.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase()))

      const matchesType = selectedType === 'all' || item.item_type === selectedType
      const matchesLocation = selectedLocation === 'all' || item.location === selectedLocation
      const matchesStatus = statusFilter === 'all' || item.status === statusFilter

      return matchesSearch && matchesType && matchesLocation && matchesStatus
    })
  }, [items, searchTerm, selectedType, selectedLocation, statusFilter])

  // 排序和分页处理
  const sortedItems = useMemo(() => {
    if (!sortDescriptor.column) return filteredItems

    return [...filteredItems].sort((a, b) => {
      const firstValue = a[sortDescriptor.column as keyof SNMPItem]
      const secondValue = b[sortDescriptor.column as keyof SNMPItem]

      // 处理可能为undefined的情况
      const first = firstValue !== undefined ? String(firstValue) : ''
      const second = secondValue !== undefined ? String(secondValue) : ''

      const cmp = first < second ? -1 : first > second ? 1 : 0

      return sortDescriptor.direction === "descending" ? -cmp : cmp
    })
  }, [filteredItems, sortDescriptor])

  // 分页数据
  const paginatedItems = useMemo(() => {
    const start = (page - 1) * rowsPerPage
    const end = start + rowsPerPage
    return sortedItems.slice(start, end)
  }, [sortedItems, page, rowsPerPage])

  // 获取所有可用的类型和位置
  const itemTypes = useMemo(() =>
    ['all', ...Array.from(new Set(items.map(item => item.item_type)))],
    [items]
  )

  const locations = useMemo(() =>
    ['all', ...Array.from(new Set(items.map(item => item.location)))],
    [items]
  )

  // 打开添加监控项模态框
  const handleOpenAddModal = () => {
    setCurrentItem(undefined)
    setModalTitle('添加SNMP监控项')
    setIsModalOpen(true)
  }

  // 打开编辑监控项模态框
  const handleOpenEditModal = (item: SNMPItem) => {
    setCurrentItem(item)
    setModalTitle('编辑SNMP监控项')
    setIsModalOpen(true)
  }

  // 关闭模态框
  const handleCloseModal = () => {
    setIsModalOpen(false)
  }

  // 保存监控项
  const handleSaveItem = async (itemData: Partial<SNMPItem>) => {
    try {
      if (currentItem?.id) {
        const updatedItem = await updateSNMPItem(currentItem.id, itemData)
        if (updatedItem) {
          setItems(items.map(item => item.id === currentItem.id ? updatedItem : item))
          setIsModalOpen(false)
        } else {
          setError('更新监控项失败')
        }
      } else {
        const newItem = await createSNMPItem(itemData as Omit<SNMPItem, 'id' | 'created_at' | 'updated_at'>)
        if (newItem) {
          setItems([...items, newItem])
          setIsModalOpen(false)
        } else {
          setError('创建监控项失败')
        }
      }
    } catch (err) {
      setError('保存监控项失败')
      console.error(err)
    }
  }

  // 处理删除监控项
  const handleDelete = async (id: number) => {
    if (window.confirm('确定要删除这个监控项吗？')) {
      try {
        const success = await deleteSNMPItem(id)
        if (success) {
          setItems(items.filter(item => item.id !== id))
        } else {
          setError('删除监控项失败')
        }
      } catch (err) {
        setError('删除监控项失败')
        console.error(err)
      }
    }
  }

  // 状态颜色映射
  const statusColorMap: Record<string, 'success' | 'danger' | 'warning'> = {
    active: 'success',
    inactive: 'danger'
  }

  // 类型颜色映射
  const typeColorMap: Record<string, string> = {
    temperature: 'bg-blue-100 text-blue-800',
    humidity: 'bg-green-100 text-green-800',
    smoke: 'bg-red-100 text-red-800',
    water: 'bg-cyan-100 text-cyan-800',
    other: 'bg-gray-100 text-gray-800'
  }

  // 这些函数已经通过内联方式使用，不需要单独定义

  // 处理搜索框清除
  const handleClearSearch = () => {
    setSearchTerm('');
  };

  // 处理类型选择变化
  const handleTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedType(e.target.value);
  };

  // 处理位置选择变化
  const handleLocationChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedLocation(e.target.value);
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <Card className="mb-6 shadow-lg">
        <CardHeader className="flex justify-between items-center bg-gradient-to-r from-blue-500 to-cyan-500 text-white">
          <div>
            <h1 className="text-2xl font-bold">SNMP监控配置管理</h1>
            <p className="text-white text-opacity-80">管理数据中心和弱电间的SNMP监控项配置</p>
          </div>
          <Button
            color="default"
            startContent={<PlusIcon />}
            onClick={handleOpenAddModal}
            className="shadow-md bg-white text-blue-600 font-medium"
          >
            添加监控项
          </Button>
        </CardHeader>
        <Divider />
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <Input
              isClearable
              placeholder="搜索名称、键值、OID或描述"
              startContent={<SearchIcon className="text-gray-400" />}
              value={searchTerm}
              onClear={handleClearSearch}
              onValueChange={setSearchTerm}
              variant="bordered"
              size="md"
              className="w-full"
            />
            <Select
              label="监控项类型"
              selectedKeys={[selectedType]}
              onChange={handleTypeChange}
              variant="bordered"
              size="md"
              startContent={<Chip size="sm" className="bg-blue-100 text-blue-800">类型</Chip>}
            >
              {itemTypes.map(type => (
                <SelectItem key={type} value={type}>
                  {type === 'all' ? '全部类型' : type}
                </SelectItem>
              ))}
            </Select>
            <Select
              label="位置"
              selectedKeys={[selectedLocation]}
              onChange={handleLocationChange}
              variant="bordered"
              size="md"
              startContent={<Chip size="sm" className="bg-green-100 text-green-800">位置</Chip>}
            >
              {locations.map(location => (
                <SelectItem key={location} value={location}>
                  {location === 'all' ? '全部位置' : location}
                </SelectItem>
              ))}
            </Select>
          </div>

          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}

          <div className="flex justify-between items-center mb-4">
            <p className="text-sm text-gray-500">
              共 <span className="font-semibold">{filteredItems.length}</span> 个监控项 (总计 {items.length} 个)
            </p>
            <Dropdown>
              <DropdownTrigger>
                <Button variant="bordered" startContent={<FilterIcon />}>
                  状态筛选
                </Button>
              </DropdownTrigger>
              <DropdownMenu
                aria-label="Filter options"
                onAction={(key: React.Key) => setStatusFilter(key.toString())}
              >
                <DropdownItem key="all">显示全部</DropdownItem>
                <DropdownItem key="active">仅显示活跃</DropdownItem>
                <DropdownItem key="inactive">仅显示禁用</DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </div>
        </CardBody>
      </Card>

      <Card className="shadow-lg">
        <CardBody>
          {loading ? (
            <div className="flex justify-center py-8">
              <Spinner size="lg" />
            </div>
          ) : filteredItems.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">没有找到匹配的监控项</p>
              <Button
                color="primary"
                className="mt-4"
                onClick={handleOpenAddModal}
              >
                添加第一个监控项
              </Button>
            </div>
          ) : (
            <div>
              <Table
                aria-label="SNMP监控项列表"
                classNames={{
                  wrapper: "shadow-none"
                }}
                sortDescriptor={sortDescriptor}
                onSortChange={(descriptor: SortDescriptor) => setSortDescriptor(descriptor)}
              >
                <TableHeader>
                  <TableColumn key="name" allowsSorting>名称</TableColumn>
                  <TableColumn key="device_id" allowsSorting>设备ID</TableColumn>
                  <TableColumn key="key">键值/OID</TableColumn>
                  <TableColumn key="item_type" allowsSorting>类型</TableColumn>
                  <TableColumn key="location" allowsSorting>位置</TableColumn>
                  <TableColumn key="status" allowsSorting>状态</TableColumn>
                  <TableColumn>操作</TableColumn>
                </TableHeader>
                <TableBody>
                  {paginatedItems.map((item) => (
                    <TableRow key={item.id} className="hover:bg-gray-50 transition-colors">
                      <TableCell>
                        <div className="font-medium">{item.name}</div>
                        <div className="text-sm text-gray-500">{item.description}</div>
                      </TableCell>
                      <TableCell>{item.device_id}</TableCell>
                      <TableCell>
                        <div className="font-mono text-sm">{item.key}</div>
                        <div className="font-mono text-xs text-gray-500">{item.oid}</div>
                      </TableCell>
                      <TableCell>
                        <Chip
                          className={`${typeColorMap[item.item_type] || 'bg-gray-100 text-gray-800'}`}
                          size="sm"
                        >
                          {item.item_type}
                        </Chip>
                      </TableCell>
                      <TableCell>
                        {item.location} {item.position && `(${item.position})`}
                      </TableCell>
                      <TableCell>
                        <Badge
                          color={statusColorMap[item.status]}
                          variant="flat"
                        >
                          {item.status === 'active' ? '活跃' : '禁用'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Tooltip content="编辑">
                            <Button isIconOnly size="sm" variant="light" onClick={() => handleOpenEditModal(item)}>
                              <EditIcon className="text-blue-500" />
                            </Button>
                          </Tooltip>
                          <Tooltip content="删除">
                            <Button isIconOnly size="sm" variant="light" onClick={() => handleDelete(item.id)}>
                              <DeleteIcon className="text-danger" />
                            </Button>
                          </Tooltip>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              <div className="flex justify-between items-center mt-4">
                <span className="text-sm text-gray-500">
                  第 {Math.min((page - 1) * rowsPerPage + 1, filteredItems.length)} - {Math.min(page * rowsPerPage, filteredItems.length)} 项，共 {filteredItems.length} 项
                </span>
                <Pagination
                  total={Math.ceil(filteredItems.length / rowsPerPage)}
                  page={page}
                  onChange={setPage}
                  showControls
                />
              </div>
            </div>
          )}
        </CardBody>
        <CardFooter className="flex justify-between bg-gray-50">
          <p className="text-sm text-gray-500">
            最后更新时间: {new Date().toLocaleString()}
          </p>
          <Button
            color="primary"
            variant="light"
            onClick={handleOpenAddModal}
          >
            添加监控项
          </Button>
        </CardFooter>
      </Card>

      <SNMPItemModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onSave={handleSaveItem}
        item={currentItem}
        title={modalTitle}
      />
    </div>
  )
}
