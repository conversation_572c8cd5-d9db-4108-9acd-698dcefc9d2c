from sqlalchemy import Column, Integer, String, Float, <PERSON><PERSON><PERSON>, DateTime, Date, Text, ForeignKey, JSO<PERSON>, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum
from datetime import datetime, date
from typing import Optional

from database import Base

class LifecycleStatus(enum.Enum):
    PLANNING = "planning"  # 规划中
    PROCUREMENT = "procurement"  # 采购中
    DEPLOYMENT = "deployment"  # 部署中
    PRODUCTION = "production"  # 生产环境
    MAINTENANCE = "maintenance"  # 维护中
    RETIRED = "retired"  # 已退役
    DISPOSED = "disposed"  # 已处置

class DepreciationMethod(enum.Enum):
    STRAIGHT_LINE = "straight_line"  # 直线法
    DECLINING_BALANCE = "declining_balance"  # 余额递减法
    UNITS_OF_PRODUCTION = "units_of_production"  # 工作量法

class AssetStatus(enum.Enum):
    ACTIVE = "active"  # 活跃
    INACTIVE = "inactive"  # 非活跃
    MAINTENANCE = "maintenance"  # 维护中
    FAULTY = "faulty"  # 故障
    RETIRED = "retired"  # 已退役
    SCRAPPED = "scrapped"  # 报废
    STORED = "stored"  # 存放

class StorageType(enum.Enum):
    HDD = "hdd"  # 机械硬盘
    SSD = "ssd"  # 固态硬盘
    NVME = "nvme"  # NVMe固态硬盘
    HYBRID = "hybrid"  # 混合硬盘

class SecurityLevel(enum.Enum):
    PUBLIC = "public"  # 公开
    INTERNAL = "internal"  # 内部
    CONFIDENTIAL = "confidential"  # 机密
    RESTRICTED = "restricted"  # 限制

class AssetCategory(Base):
    __tablename__ = "asset_categories"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    code = Column(String(20), nullable=True)
    description = Column(Text, nullable=True)
    level = Column(Integer, default=1)
    parent_id = Column(Integer, ForeignKey("asset_categories.id"), nullable=True)
    attributes = Column(JSON, nullable=True)
    is_system = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系
    assets = relationship("Asset", back_populates="category")
    children = relationship("AssetCategory", backref="parent", remote_side=[id])

class Asset(Base):
    __tablename__ = "assets"

    id = Column(Integer, primary_key=True, index=True)
    # 基础信息
    name = Column(String(255), nullable=False, comment="资产名称")
    asset_code = Column(String(100), unique=True, nullable=False, comment="资产编码")
    model = Column(String(255), comment="型号")
    category_id = Column(Integer, ForeignKey('asset_categories.id'), nullable=False, comment="分类ID")
    status = Column(Enum(AssetStatus), default=AssetStatus.ACTIVE, comment="资产状态")
    
    # 采购信息
    purchase_date = Column(Date, comment="购买日期")
    purchase_price = Column(Float, comment="购买价格")
    lifecycle_status = Column(Enum(LifecycleStatus), default=LifecycleStatus.PLANNING, comment="生命周期状态")
    warranty_start_date = Column(Date, comment="保修开始日期")
    warranty_end_date = Column(Date, comment="保修到期日期")
    manufacturer = Column(String(255), comment="制造商")
    supplier = Column(String(255), comment="供应商")
    serial_number = Column(String(255), unique=True, comment="序列号")
    asset_tag = Column(String(100), comment="资产标签")
    
    # 折旧信息
    depreciation_method = Column(Enum(DepreciationMethod), comment="折旧方法")
    depreciation_years = Column(Integer, comment="折旧年限")
    current_value = Column(Float, comment="当前价值")
    residual_value = Column(Float, comment="残值")
    
    description = Column(Text, nullable=True)
    purchase_order_number = Column(String(50), nullable=True)
    asset_value = Column(Float, nullable=True)
    depreciation_period = Column(Integer, nullable=True)  # 折旧年限(月)
    depreciation_rate = Column(Float, nullable=True)  # 折旧率
    last_check_date = Column(DateTime, nullable=True)
    responsible_person = Column(String(255), comment="负责人")
    department = Column(String(255), comment="部门")
    
    # IT网络资产专用字段
    ip_address = Column(String(45), comment="IP地址")
    business_ip_address = Column(String(45), comment="业务IP地址")
    management_ip_address = Column(String(45), comment="管理IP地址")
    mac_address = Column(String(17), comment="MAC地址")
    hostname = Column(String(255), comment="主机名")
    domain = Column(String(255), comment="域名")
    subnet_mask = Column(String(15), comment="子网掩码")
    gateway = Column(String(45), comment="网关")
    dns_servers = Column(JSON, comment="DNS服务器")
    
    # 硬件规格
    cpu_model = Column(String(255), comment="CPU型号")
    cpu_cores = Column(Integer, comment="CPU核心数")
    cpu_frequency = Column(Float, comment="CPU频率(GHz)")
    memory_size = Column(Integer, comment="内存大小(GB)")
    memory_type = Column(String(50), comment="内存类型")
    storage_size = Column(Integer, comment="存储大小(GB)")
    storage_type = Column(Enum(StorageType), comment="存储类型")
    graphics_card = Column(String(255), comment="显卡")
    
    # 网络配置
    network_ports = Column(Integer, comment="网络端口数")
    port_speed = Column(String(50), comment="端口速度")
    power_consumption = Column(Float, comment="功耗(W)")
    power_supply = Column(String(100), comment="电源规格")
    
    # 软件信息
    operating_system = Column(String(255), comment="操作系统")
    os_version = Column(String(100), comment="操作系统版本")
    firmware_version = Column(String(100), comment="固件版本")
    firmware_update_date = Column(Date, comment="固件更新日期")
    firmware_vendor = Column(String(255), comment="固件厂商")
    software_licenses = Column(JSON, comment="软件许可证信息")
    installed_software = Column(JSON, comment="已安装软件")
    
    # 网络设备专用字段
    device_type = Column(String(100), comment="设备类型")
    port_configuration = Column(JSON, comment="端口配置信息")
    vlan_support = Column(Boolean, default=False, comment="是否支持VLAN")
    routing_protocols = Column(JSON, comment="支持的路由协议")
    management_interface = Column(String(100), comment="管理接口类型")
    
    # 终端设备专用字段
    screen_size = Column(String(50), comment="屏幕尺寸")
    screen_resolution = Column(String(50), comment="屏幕分辨率")
    battery_capacity = Column(String(50), comment="电池容量")
    wireless_support = Column(JSON, comment="无线支持(WiFi/蓝牙等)")
    peripheral_ports = Column(JSON, comment="外设接口")
    
    # 服务器业务信息
    business_services = Column(JSON, comment="业务服务列表")
    service_ports = Column(JSON, comment="服务端口映射")
    business_ports = Column(JSON, comment="业务端口配置")
    cluster_info = Column(JSON, comment="集群信息")
    virtualization_platform = Column(String(100), comment="虚拟化平台")
    
    # 合同资产专用字段
    contract_number = Column(String(100), comment="合同编号")
    contract_start_date = Column(Date, comment="合同开始日期")
    contract_end_date = Column(Date, comment="合同到期日期")
    contract_amount = Column(Float, comment="合同金额")
    contract_vendor = Column(String(255), comment="合同供应商")
    contract_type = Column(String(100), comment="合同类型")
    auto_renewal = Column(Boolean, default=False, comment="是否自动续约")
    
    # 维保资产专用字段
    maintenance_level = Column(String(50), comment="维保级别")
    response_time = Column(String(50), comment="响应时间")
    maintenance_scope = Column(Text, comment="维保范围")
    maintenance_start_date = Column(Date, comment="维保开始日期")
    maintenance_end_date = Column(Date, comment="维保到期日期")
    
    # 授权信息
    license_type = Column(String(100), comment="许可证类型")
    license_key = Column(String(500), comment="许可证密钥")
    license_start_date = Column(Date, comment="授权开始日期")
    license_end_date = Column(Date, comment="授权到期日期")
    license_user_count = Column(Integer, comment="授权用户数")
    license_concurrent_users = Column(Integer, comment="并发用户数")
    
    # 安全相关
    security_level = Column(Enum(SecurityLevel), default=SecurityLevel.INTERNAL, comment="安全级别")
    encryption_enabled = Column(Boolean, default=False, comment="是否启用加密")
    firewall_enabled = Column(Boolean, default=False, comment="是否启用防火墙")
    antivirus_software = Column(String(255), comment="杀毒软件")
    last_security_scan = Column(Date, comment="最后安全扫描日期")
    
    # 安防设备专用字段
    security_device_type = Column(String(100), comment="安防设备类型")
    video_resolution = Column(String(50), comment="视频分辨率")
    recording_capacity = Column(Integer, comment="录像容量(GB)")
    night_vision = Column(Boolean, default=False, comment="是否支持夜视")
    motion_detection = Column(Boolean, default=False, comment="是否支持动态检测")
    audio_support = Column(Boolean, default=False, comment="是否支持音频")
    ptz_support = Column(Boolean, default=False, comment="是否支持云台控制")
    weatherproof_rating = Column(String(20), comment="防护等级")
    power_over_ethernet = Column(Boolean, default=False, comment="是否支持PoE供电")
    
    # 耗材相关字段
    is_consumable = Column(Boolean, default=False, comment="是否为耗材")
    quantity = Column(Integer, comment="数量")
    unit = Column(String(20), comment="单位")
    min_stock_level = Column(Integer, comment="最低库存")
    max_stock_level = Column(Integer, comment="最高库存")
    reorder_point = Column(Integer, comment="补货点")
    expiry_date = Column(Date, comment="过期日期")
    batch_number = Column(String(100), comment="批次号")
    
    # 位置信息
    location = Column(String(255), comment="位置")
    building = Column(String(100), comment="建筑物")
    floor = Column(String(50), comment="楼层")
    room = Column(String(100), comment="房间")
    rack_id = Column(String(50), comment="机架ID")
    rack_position = Column(String(50), comment="机架位置")
    rack_unit = Column(Integer, comment="机架单元数")
    
    # 管理信息
    cost_center = Column(String(100), comment="成本中心")
    project_code = Column(String(100), comment="项目代码")
    business_unit = Column(String(255), comment="业务单元")
    
    # 维护信息
    last_maintenance_date = Column(Date, comment="最后维护日期")
    next_maintenance_date = Column(Date, comment="下次维护日期")
    maintenance_contract = Column(String(255), comment="维护合同")
    maintenance_vendor = Column(String(255), comment="维护供应商")
    
    # 监控配置
    monitoring_enabled = Column(Boolean, default=False, comment="是否启用监控")
    snmp_enabled = Column(Boolean, default=False, comment="是否启用SNMP")
    snmp_community = Column(String(100), comment="SNMP团体名")
    snmp_version = Column(String(10), comment="SNMP版本")
    ssh_enabled = Column(Boolean, default=False, comment="是否启用SSH")
    ssh_port = Column(Integer, default=22, comment="SSH端口")
    
    # 其他信息
    notes = Column(Text, comment="备注")
    tags = Column(JSON, comment="标签")
    custom_fields = Column(JSON, comment="自定义字段")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关系
    category = relationship("AssetCategory", back_populates="assets")
    maintenance_records = relationship("MaintenanceRecord", back_populates="asset")

class MaintenanceRecord(Base):
    __tablename__ = "maintenance_records"

    id = Column(Integer, primary_key=True, index=True)
    asset_id = Column(Integer, ForeignKey("assets.id"), nullable=False)
    date = Column(DateTime, nullable=False, default=datetime.utcnow)
    type = Column(String(20), nullable=False)  # routine, repair, upgrade
    description = Column(Text, nullable=False)
    cost = Column(Float, nullable=True)
    performed_by = Column(String(100), nullable=True)
    next_scheduled_date = Column(DateTime, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    asset = relationship("Asset", back_populates="maintenance_records")
