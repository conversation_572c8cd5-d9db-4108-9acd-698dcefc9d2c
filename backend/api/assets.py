from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update, delete
from typing import List, Optional
import logging
from datetime import datetime

from database import get_db
from models.asset import Asset, AssetCategory, MaintenanceRecord
from schemas.asset import (
    AssetCreate, AssetUpdate, AssetResponse, 
    AssetCategoryCreate, AssetCategoryUpdate, AssetCategoryResponse,
    MaintenanceRecordCreate, MaintenanceRecordResponse
)
from .utils import generic_update_item_async

router = APIRouter(prefix="/api", tags=["assets"])
logger = logging.getLogger(__name__)

# 资产分类API端点
@router.post("/asset-categories", response_model=AssetCategoryResponse)
async def create_asset_category(
    category: AssetCategoryCreate, 
    db: AsyncSession = Depends(get_db)
):
    """创建新的资产分类"""
    try:
        # 检查编码是否唯一
        result = await db.execute(select(AssetCategory).filter(AssetCategory.code == category.code))
        existing_category = result.scalars().first()
        if existing_category:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Category code already exists"
            )
        
        db_category = AssetCategory(**category.dict())
        db.add(db_category)
        await db.commit()
        await db.refresh(db_category)
        return db_category
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建资产分类失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建资产分类失败: {str(e)}"
        )

@router.get("/asset-categories", response_model=List[AssetCategoryResponse])
async def get_asset_categories(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    parent_id: Optional[int] = None,
    db: AsyncSession = Depends(get_db)
):
    """获取资产分类列表"""
    try:
        query = select(AssetCategory)
        
        if parent_id is not None:
            query = query.filter(AssetCategory.parent_id == parent_id)
        
        query = query.offset(skip).limit(limit)
        result = await db.execute(query)
        categories = result.scalars().all()
        return categories
    except Exception as e:
        logger.error(f"获取资产分类失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取资产分类失败: {str(e)}"
        )

@router.get("/asset-categories/{category_id}", response_model=AssetCategoryResponse)
async def get_asset_category(category_id: int, db: AsyncSession = Depends(get_db)):
    """获取特定资产分类"""
    try:
        result = await db.execute(select(AssetCategory).filter(AssetCategory.id == category_id))
        category = result.scalars().first()
        if not category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"ID为{category_id}的资产分类不存在"
            )
        return category
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取资产分类失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取资产分类失败: {str(e)}"
        )

@router.put("/asset-categories/{category_id}", response_model=AssetCategoryResponse)
async def update_asset_category(
    category_id: int, 
    category_update: AssetCategoryUpdate, 
    db: AsyncSession = Depends(get_db)
):
    """更新资产分类"""
    return await generic_update_item_async(
        db=db,
        model_cls=AssetCategory,
        item_id=category_id,
        update_data_schema=category_update,
        item_name="资产分类"
    )

@router.delete("/asset-categories/{category_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_asset_category(category_id: int, db: AsyncSession = Depends(get_db)):
    """删除资产分类"""
    try:
        # 检查分类是否存在
        result = await db.execute(select(AssetCategory).filter(AssetCategory.id == category_id))
        db_category = result.scalars().first()
        if not db_category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"ID为{category_id}的资产分类不存在"
            )
        
        # 检查是否有资产使用此分类
        result = await db.execute(select(Asset).filter(Asset.category_id == category_id))
        if result.scalars().first():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无法删除分类，因为有资产正在使用此分类"
            )
        
        # 删除分类
        await db.execute(delete(AssetCategory).where(AssetCategory.id == category_id))
        await db.commit()
        return None
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除资产分类失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除资产分类失败: {str(e)}"
        )

# 资产API端点
@router.post("/assets", response_model=AssetResponse)
async def create_asset(asset: AssetCreate, db: AsyncSession = Depends(get_db)):
    """创建新资产"""
    try:
        # 检查分类是否存在
        result = await db.execute(select(AssetCategory).filter(AssetCategory.id == asset.category_id))
        if not result.scalars().first():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"ID为{asset.category_id}的资产分类不存在"
            )
        
        # 检查资产编码是否唯一
        result = await db.execute(select(Asset).filter(Asset.code == asset.code))
        if result.scalars().first():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"资产编码'{asset.code}'已存在"
            )
        
        # 创建资产
        db_asset = Asset(**asset.dict())
        db.add(db_asset)
        await db.commit()
        await db.refresh(db_asset)
        return db_asset
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建资产失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建资产失败: {str(e)}"
        )

@router.get("/assets", response_model=List[AssetResponse])
async def get_assets(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    category_id: Optional[int] = None,
    status: Optional[str] = None,
    search: Optional[str] = None,
    location: Optional[str] = None,
    department: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """获取资产列表，支持多种筛选条件"""
    try:
        query = select(Asset)
        
        if category_id:
            query = query.filter(Asset.category_id == category_id)
        if status:
            query = query.filter(Asset.lifecycle_status == status)
        if location:
            query = query.filter(Asset.location.ilike(f"%{location}%"))
        if department:
            query = query.filter(Asset.department.ilike(f"%{department}%"))
        if search:
            search_filter = (
                Asset.name.ilike(f"%{search}%") |
                Asset.code.ilike(f"%{search}%") |
                Asset.serial_number.ilike(f"%{search}%") |
                Asset.manufacturer.ilike(f"%{search}%") |
                Asset.model.ilike(f"%{search}%")
            )
            query = query.filter(search_filter)
        
        query = query.offset(skip).limit(limit)
        result = await db.execute(query)
        assets = result.scalars().all()
        return assets
    except Exception as e:
        logger.error(f"获取资产列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取资产列表失败: {str(e)}"
        )

@router.get("/assets/{asset_id}", response_model=AssetResponse)
async def get_asset(asset_id: int, db: AsyncSession = Depends(get_db)):
    """获取特定资产详情"""
    try:
        result = await db.execute(select(Asset).filter(Asset.id == asset_id))
        asset = result.scalars().first()
        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"ID为{asset_id}的资产不存在"
            )
        return asset
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取资产详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取资产详情失败: {str(e)}"
        )

async def _pre_update_asset_checks(db: AsyncSession, db_item: Asset, update_schema: AssetUpdate):
    """资产更新前的特定检查。"""
    # 1. 检查分类是否存在 (如果提供了 category_id 且与现有 category_id 不同)
    if update_schema.category_id is not None and update_schema.category_id != db_item.category_id:
        category_result = await db.execute(select(AssetCategory).filter(AssetCategory.id == update_schema.category_id))
        if not category_result.scalars().first():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"ID为{update_schema.category_id}的资产分类不存在"
            )

    # 2. 检查资产编码是否唯一 (如果提供了 code 且与现有 code 不同)
    if update_schema.code is not None and update_schema.code != db_item.code:
        # 检查是否有其他资产（非当前资产）已使用新编码
        existing_asset_with_new_code = await db.execute(
            select(Asset).filter(Asset.code == update_schema.code, Asset.id != db_item.id)
        )
        if existing_asset_with_new_code.scalars().first():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"资产编码'{update_schema.code}'已被其他资产使用"
            )

@router.put("/assets/{asset_id}", response_model=AssetResponse)
async def update_asset(
    asset_id: int, 
    asset_update: AssetUpdate, 
    db: AsyncSession = Depends(get_db)
):
    """更新资产信息"""
    return await generic_update_item_async(
        db=db,
        model_cls=Asset,
        item_id=asset_id,
        update_data_schema=asset_update,
        item_name="资产",
        pre_update_checks=_pre_update_asset_checks
    )

@router.delete("/assets/{asset_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_asset(asset_id: int, db: AsyncSession = Depends(get_db)):
    """删除资产"""
    try:
        # 检查资产是否存在
        result = await db.execute(select(Asset).filter(Asset.id == asset_id))
        db_asset = result.scalars().first()
        if not db_asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"ID为{asset_id}的资产不存在"
            )
        
        # 删除资产
        await db.execute(delete(Asset).where(Asset.id == asset_id))
        await db.commit()
        return None
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除资产失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除资产失败: {str(e)}"
        )

@router.put("/maintenance-records/{record_id}", response_model=MaintenanceRecordResponse)
async def update_maintenance_record(
    record_id: int, 
    record_update: MaintenanceRecordCreate, # Assuming MaintenanceRecordCreate can be used for updates or a specific MaintenanceRecordUpdate schema exists
    db: AsyncSession = Depends(get_db)
):
    """更新维保记录"""
    # 预更新检查：检查关联的资产是否存在
    async def _pre_update_maintenance_checks(db_session: AsyncSession, db_item: MaintenanceRecord, update_schema: MaintenanceRecordCreate):
        if update_schema.asset_id and update_schema.asset_id != db_item.asset_id:
            asset_result = await db_session.execute(select(Asset).filter(Asset.id == update_schema.asset_id))
            if not asset_result.scalars().first():
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"ID为{update_schema.asset_id}的资产不存在"
                )

    return await generic_update_item_async(
        db=db,
        model_cls=MaintenanceRecord,
        item_id=record_id,
        update_data_schema=record_update,
        item_name="维保记录",
        pre_update_checks=_pre_update_maintenance_checks
    )

# 更多资产相关API...
